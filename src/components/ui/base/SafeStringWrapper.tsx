"use client";

import { ReactNode, Component, ErrorInfo } from 'react';
import { useRouter } from 'next/navigation';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  redirectOnError?: boolean;
}

interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * Wrapper component specifically for handling startsWith and string-related errors
 */
class SafeStringWrapper extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Check if it's a startsWith related error
    if (error.message.includes("Cannot read properties of undefined (reading 'startsWith')") ||
        error.message.includes("Cannot read property 'startsWith' of undefined")) {
      return {
        hasError: true,
        error,
      };
    }
    
    // Check for other string method errors
    if (error.message.includes("Cannot read properties of undefined (reading") ||
        error.message.includes("Cannot read property") ||
        (error.name === 'TypeError' && error.message.includes('undefined'))) {
      return {
        hasError: true,
        error,
      };
    }
    
    // Don't catch other types of errors
    throw error;
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('SafeStringWrapper caught an error:', error, errorInfo);
    
    // Auto-redirect for startsWith errors
    if (this.props.redirectOnError !== false) {
      setTimeout(() => {
        if (typeof window !== 'undefined') {
          window.location.href = '/not-found';
        }
      }, 2000);
    }
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return <SafeStringErrorFallback error={this.state.error} />;
    }

    return this.props.children;
  }
}

/**
 * Fallback component for string-related errors
 */
function SafeStringErrorFallback({ error }: { error?: Error }) {
  const isStartsWithError = error?.message.includes("Cannot read properties of undefined (reading 'startsWith')") ||
                           error?.message.includes("Cannot read property 'startsWith' of undefined");

  return (
    <div className="min-h-[200px] flex items-center justify-center bg-gray-50 rounded-lg p-6">
      <div className="text-center">
        <div className="mb-4">
          <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {isStartsWithError ? 'Data Tidak Valid' : 'Kesalahan Pemrosesan'}
          </h3>
          <p className="text-gray-600 text-sm">
            {isStartsWithError 
              ? 'Terjadi kesalahan dalam memproses data halaman. Anda akan dialihkan ke halaman utama.'
              : 'Terjadi kesalahan dalam memproses data. Silakan coba lagi.'
            }
          </p>
        </div>
        
        {/* Loading indicator for redirect */}
        {isStartsWithError && (
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-default"></div>
            <span>Mengalihkan...</span>
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * HOC for wrapping components that might have string-related errors
 */
export function withSafeString<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options?: {
    fallback?: ReactNode;
    redirectOnError?: boolean;
  }
) {
  const SafeComponent = (props: P) => {
    return (
      <SafeStringWrapper 
        fallback={options?.fallback}
        redirectOnError={options?.redirectOnError}
      >
        <WrappedComponent {...props} />
      </SafeStringWrapper>
    );
  };

  SafeComponent.displayName = `withSafeString(${WrappedComponent.displayName || WrappedComponent.name})`;
  
  return SafeComponent;
}

/**
 * Hook for safe string operations with error handling
 */
export function useSafeStringOperations() {
  const safeStartsWith = (str: string | undefined | null, searchString: string) => {
    try {
      if (!str || typeof str !== 'string') {
        return false;
      }
      return str.startsWith(searchString);
    } catch (error) {
      console.warn('Error in safeStartsWith:', error);
      return false;
    }
  };

  const safeEndsWith = (str: string | undefined | null, searchString: string) => {
    try {
      if (!str || typeof str !== 'string') {
        return false;
      }
      return str.endsWith(searchString);
    } catch (error) {
      console.warn('Error in safeEndsWith:', error);
      return false;
    }
  };

  const safeIncludes = (str: string | undefined | null, searchString: string) => {
    try {
      if (!str || typeof str !== 'string') {
        return false;
      }
      return str.includes(searchString);
    } catch (error) {
      console.warn('Error in safeIncludes:', error);
      return false;
    }
  };

  const safeSplit = (str: string | undefined | null, separator: string | RegExp) => {
    try {
      if (!str || typeof str !== 'string') {
        return [];
      }
      return str.split(separator);
    } catch (error) {
      console.warn('Error in safeSplit:', error);
      return [];
    }
  };

  return {
    safeStartsWith,
    safeEndsWith,
    safeIncludes,
    safeSplit,
  };
}

export default SafeStringWrapper;
