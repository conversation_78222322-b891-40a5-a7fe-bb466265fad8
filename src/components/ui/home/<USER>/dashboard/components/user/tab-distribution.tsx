"use client";

import { useState } from "react";

export default function TabDistribution() {
  const [stats] = useState({
    total: 8,
    scheduled: 3,
    inProgress: 2,
    completed: 3,
  });

  return (
    <div className="mt-4">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
          <div className="text-sm text-blue-600">Total Distribusi</div>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-yellow-600">{stats.scheduled}</div>
          <div className="text-sm text-yellow-600">Di<PERSON><PERSON><PERSON>an</div>
        </div>
        <div className="bg-orange-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-orange-600">{stats.inProgress}</div>
          <div className="text-sm text-orange-600"><PERSON><PERSON> Proses</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
          <div className="text-sm text-green-600">Selesai</div>
        </div>
      </div>
    </div>
  );
}
