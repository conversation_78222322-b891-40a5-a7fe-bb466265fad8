import { usePathname } from 'next/navigation';
import { useMemo } from 'react';
import { safeStartsWith, safeSplit, validatePathname } from '@/lib/utils/stringUtils';

interface BreadcrumbItem {
  name: string;
  path: string;
  isLast?: boolean;
}

/**
 * Safe pathname hook that prevents startsWith errors
 */
export function useSafePathname() {
  const pathname = usePathname();
  
  const safePathname = useMemo(() => {
    return validatePathname(pathname);
  }, [pathname]);
  
  const pathSegments = useMemo(() => {
    return safeSplit(safePathname, '/').filter(Boolean);
  }, [safePathname]);
  
  const safeStartsWithCheck = (searchString: string) => {
    return safeStartsWith(safePathname, searchString);
  };
  
  const getSegment = (index: number): string => {
    return pathSegments[index] || '';
  };
  
  const getLastSegment = (): string => {
    return pathSegments[pathSegments.length - 1] || '';
  };
  
  const isExactPath = (path: string): boolean => {
    return safePathname === path;
  };
  
  const containsSegment = (segment: string): boolean => {
    return pathSegments.includes(segment);
  };
  
  return {
    pathname: safePathname,
    segments: pathSegments,
    startsWith: safeStartsWithCheck,
    getSegment,
    getLastSegment,
    isExactPath,
    containsSegment,
    isValid: Boolean(safePathname && safePathname !== '/'),
  };
}

/**
 * Hook for safe breadcrumb generation
 */
export function useSafeBreadcrumb() {
  const { pathname, segments } = useSafePathname();
  
  const breadcrumbs = useMemo((): BreadcrumbItem[] => {
    if (!segments.length) {
      return [{ name: 'Home', path: '/' }];
    }

    const crumbs: BreadcrumbItem[] = [{ name: 'Home', path: '/' }];
    let currentPath = '';

    segments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const name = segment.charAt(0).toUpperCase() + segment.slice(1);
      crumbs.push({
        name,
        path: currentPath,
        isLast: index === segments.length - 1
      });
    });

    return crumbs;
  }, [segments]);
  
  return {
    breadcrumbs,
    currentPage: segments[segments.length - 1] || 'Home'
  };
}

/**
 * Hook for safe navigation checks
 */
export function useSafeNavigation() {
  const { pathname, startsWith } = useSafePathname();
  
  const isHomePage = () => startsWith('/home');
  const isAuthPage = () => startsWith('/auth');
  const isAdminPage = () => startsWith('/home/<USER>') || startsWith('/home/<USER>');
  const isUserPage = () => startsWith('/home/<USER>') || startsWith('/home/<USER>');
  const isProfilePage = () => startsWith('/home/<USER>');
  const isDashboardPage = () => startsWith('/home/<USER>');
  
  return {
    pathname,
    isHomePage,
    isAuthPage,
    isAdminPage,
    isUserPage,
    isProfilePage,
    isDashboardPage,
  };
}
