import { ApiResponse, ApiResponseById } from "@/types/planting/submissionPlant";


// Fungsi untuk mengambil data dari API
export const fetchSubmissionPlantData = async (page: number, token: string): Promise<ApiResponse['data']> => {
    const res = await fetch(`/api/proxy/submissions/stok-tanaman?page=${page}`, {
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });
    const data = await res.json();
    return data.data;
};

// Fungsi untuk mengambil data dari API
export const fetchSubmissionPlantDataById = async (id: string, token: string): Promise<ApiResponseById['data']> => {
    const res = await fetch(`/api/proxy/submissions/stok-tanaman/${id}`, {
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });
    const data = await res.json();
    return data.data;
};

// Fungsi untuk Post data ke API
export const postSubmissionPlantData = async (formData: any, token: string): Promise<Response> => {
    const res = await fetch(`/api/proxy/submissions/stok-tanaman`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(formData),
    });
    const data = await res;
    return data;
};

// Fungsi untuk mengupdate data ke API
export const updateStatusSubmissionPlantData = async (payload: Object, id:string,token: string): Promise<Response> => {
    const res = await fetch(`/api/proxy/submissions/stok-tanaman/status/${id}`, {
        method: 'PUT',
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
    });
    const data = await res;
    return data;
}
