"use client";

import React, { useState } from 'react';

export const CorsFixGuide: React.FC = () => {
  const [showGuide, setShowGuide] = useState(false);

  return (
    <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-blue-800">CORS Fix Implementation</h3>
        <button
          onClick={() => setShowGuide(!showGuide)}
          className="text-blue-600 hover:text-blue-800 text-sm"
        >
          {showGuide ? 'Hide' : 'Show'} Guide
        </button>
      </div>

      <div className="text-sm text-blue-700 mb-3">
        ✅ CORS issues have been resolved by implementing API proxy routes.
      </div>

      {showGuide && (
        <div className="space-y-4 text-sm">
          <div className="bg-white p-3 rounded border">
            <h4 className="font-medium mb-2 text-blue-800">What was changed:</h4>
            <ul className="list-disc list-inside space-y-1 text-blue-700">
              <li>Created <code>/api/proxy/[...path]/route.ts</code> to handle all API requests</li>
              <li>Updated login form to use <code>/api/auth/login</code></li>
              <li>Modified fetcher functions to use proxy routes</li>
              <li>Added CORS headers in Next.js configuration</li>
            </ul>
          </div>

          <div className="bg-white p-3 rounded border">
            <h4 className="font-medium mb-2 text-blue-800">How it works:</h4>
            <ol className="list-decimal list-inside space-y-1 text-blue-700">
              <li>Frontend makes requests to <code>/api/proxy/*</code></li>
              <li>Next.js API route forwards requests to backend</li>
              <li>Backend responds to Next.js (server-to-server, no CORS)</li>
              <li>Next.js returns response to frontend</li>
            </ol>
          </div>

          <div className="bg-white p-3 rounded border">
            <h4 className="font-medium mb-2 text-blue-800">Usage examples:</h4>
            <div className="space-y-2">
              <div>
                <p className="font-medium text-blue-800">Old way (CORS error):</p>
                <pre className="text-xs bg-gray-100 p-2 rounded mt-1">
{`fetch('https://backend-simtanaman.newus.id/api/auth/login', {
  method: 'POST',
  // ... CORS error
})`}
                </pre>
              </div>
              
              <div>
                <p className="font-medium text-blue-800">New way (works):</p>
                <pre className="text-xs bg-gray-100 p-2 rounded mt-1">
{`fetch('/api/auth/login', {
  method: 'POST',
  // ... works perfectly
})`}
                </pre>
              </div>

              <div>
                <p className="font-medium text-blue-800">Using the new API client:</p>
                <pre className="text-xs bg-gray-100 p-2 rounded mt-1">
{`import { api } from '@/lib/utils/apiClient';

// GET request
const data = await api.get('profile/', token);

// POST request
const result = await api.post('auth/login', { email, password });`}
                </pre>
              </div>
            </div>
          </div>

          <div className="bg-white p-3 rounded border">
            <h4 className="font-medium mb-2 text-blue-800">Files that still need updating:</h4>
            <ul className="list-disc list-inside space-y-1 text-blue-700 text-xs">
              <li><code>src/lib/master/poktanFecthing.ts</code></li>
              <li><code>src/lib/distribution/distributionFetching.ts</code></li>
              <li><code>src/lib/master/distribusiMetodeFetching.ts</code></li>
              <li><code>src/lib/master/metodePenanamanFetching.ts</code></li>
              <li><code>src/lib/log-aktifitas/logAktifitasFetching.ts</code></li>
              <li>And other fetching files...</li>
            </ul>
            <p className="mt-2 text-blue-600">
              Replace <code>process.env.NEXT_PUBLIC_API_URL</code> with <code>/api/proxy/</code> in these files.
            </p>
          </div>

          <div className="bg-green-50 p-3 rounded border border-green-200">
            <h4 className="font-medium mb-2 text-green-800">Benefits:</h4>
            <ul className="list-disc list-inside space-y-1 text-green-700">
              <li>No more CORS errors</li>
              <li>Better security (API credentials stay on server)</li>
              <li>Centralized error handling</li>
              <li>Request/response logging capabilities</li>
              <li>Ability to add authentication middleware</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default CorsFixGuide;
