/**
 * Safe string utilities to prevent TypeError on undefined/null values
 */

/**
 * Safe startsWith check that handles undefined/null values
 * @param str - The string to check
 * @param searchString - The string to search for
 * @param position - Optional position to start the search
 * @returns boolean - true if string starts with searchString, false otherwise
 */
export function safeStartsWith(
  str: string | undefined | null, 
  searchString: string, 
  position?: number
): boolean {
  if (!str || typeof str !== 'string') {
    return false;
  }
  return str.startsWith(searchString, position);
}

/**
 * Safe endsWith check that handles undefined/null values
 * @param str - The string to check
 * @param searchString - The string to search for
 * @param length - Optional length to consider
 * @returns boolean - true if string ends with searchString, false otherwise
 */
export function safeEndsWith(
  str: string | undefined | null, 
  searchString: string, 
  length?: number
): boolean {
  if (!str || typeof str !== 'string') {
    return false;
  }
  return str.endsWith(searchString, length);
}

/**
 * Safe includes check that handles undefined/null values
 * @param str - The string to check
 * @param searchString - The string to search for
 * @param position - Optional position to start the search
 * @returns boolean - true if string includes searchString, false otherwise
 */
export function safeIncludes(
  str: string | undefined | null, 
  searchString: string, 
  position?: number
): boolean {
  if (!str || typeof str !== 'string') {
    return false;
  }
  return str.includes(searchString, position);
}

/**
 * Safe string split that handles undefined/null values
 * @param str - The string to split
 * @param separator - The separator to split by
 * @param limit - Optional limit
 * @returns string[] - array of split strings or empty array
 */
export function safeSplit(
  str: string | undefined | null, 
  separator: string | RegExp, 
  limit?: number
): string[] {
  if (!str || typeof str !== 'string') {
    return [];
  }
  return str.split(separator, limit);
}

/**
 * Safe pathname validation for routing
 * @param pathname - The pathname to validate
 * @returns string - validated pathname or default
 */
export function validatePathname(pathname: string | undefined | null): string {
  if (!pathname || typeof pathname !== 'string') {
    return '/';
  }
  
  // Ensure pathname starts with /
  if (!pathname.startsWith('/')) {
    return '/' + pathname;
  }
  
  return pathname;
}

/**
 * Safe URL validation
 * @param url - The URL to validate
 * @returns boolean - true if valid URL, false otherwise
 */
export function isValidUrl(url: string | undefined | null): boolean {
  if (!url || typeof url !== 'string') {
    return false;
  }
  
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Extract file extension safely
 * @param filename - The filename to extract extension from
 * @returns string - file extension or empty string
 */
export function getFileExtension(filename: string | undefined | null): string {
  if (!filename || typeof filename !== 'string') {
    return '';
  }
  
  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1) {
    return '';
  }
  
  return filename.substring(lastDotIndex + 1).toLowerCase();
}

/**
 * Check if file is image based on extension
 * @param filename - The filename to check
 * @returns boolean - true if image file, false otherwise
 */
export function isImageFile(filename: string | undefined | null): boolean {
  const extension = getFileExtension(filename);
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  return imageExtensions.includes(extension);
}

/**
 * Safe string truncation
 * @param str - The string to truncate
 * @param maxLength - Maximum length
 * @param suffix - Suffix to add when truncated
 * @returns string - truncated string
 */
export function safeTruncate(
  str: string | undefined | null, 
  maxLength: number, 
  suffix: string = '...'
): string {
  if (!str || typeof str !== 'string') {
    return '';
  }
  
  if (str.length <= maxLength) {
    return str;
  }
  
  return str.substring(0, maxLength - suffix.length) + suffix;
}
