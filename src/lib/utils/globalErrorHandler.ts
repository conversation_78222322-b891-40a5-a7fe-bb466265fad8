/**
 * Global error handler for unhandled errors and promise rejections
 */

/**
 * Initialize global error handlers
 */
export function initializeGlobalErrorHandlers() {
  if (typeof window === 'undefined') {
    return; // Only run on client side
  }

  // Handle unhandled JavaScript errors
  window.addEventListener('error', (event) => {
    const error = event.error;
    
    console.error('Global error caught:', error);
    
    // Check for startsWith error specifically
    if (error?.message?.includes("Cannot read properties of undefined (reading 'startsWith')") ||
        error?.message?.includes("Cannot read property 'startsWith' of undefined")) {
      console.warn('Detected global startsWith error, redirecting to 404');
      
      // Prevent default error handling
      event.preventDefault();
      
      // Redirect to 404 page
      window.location.href = '/not-found';
      return;
    }
    
    // Handle other TypeError cases that might be related to undefined values
    if (error?.name === 'TypeError' && 
        (error?.message?.includes('Cannot read properties of undefined') ||
         error?.message?.includes('Cannot read property') ||
         error?.message?.includes('undefined'))) {
      console.warn('Detected TypeError with undefined, redirecting to 404');
      
      // Prevent default error handling
      event.preventDefault();
      
      // Redirect to 404 page
      window.location.href = '/not-found';
      return;
    }
  });

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const error = event.reason;
    
    console.error('Unhandled promise rejection:', error);
    
    // Check for startsWith error in promise rejections
    if (error?.message?.includes("Cannot read properties of undefined (reading 'startsWith')") ||
        error?.message?.includes("Cannot read property 'startsWith' of undefined")) {
      console.warn('Detected promise rejection with startsWith error, redirecting to 404');
      
      // Prevent default handling
      event.preventDefault();
      
      // Redirect to 404 page
      window.location.href = '/not-found';
      return;
    }
    
    // Handle network errors in promises
    if (error?.message?.includes('Failed to fetch') ||
        error?.message?.includes('Network Error') ||
        error?.name === 'NetworkError') {
      console.warn('Network error in promise, showing error message');
      // Don't redirect for network errors, let them be handled by other error handlers
      return;
    }
  });
}

/**
 * Safe error logging function
 */
export function logError(error: unknown, context?: string) {
  const errorInfo = {
    message: error instanceof Error ? error.message : String(error),
    name: error instanceof Error ? error.name : 'Unknown',
    stack: error instanceof Error ? error.stack : undefined,
    context: context || 'Unknown',
    timestamp: new Date().toISOString(),
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'Server',
    url: typeof window !== 'undefined' ? window.location.href : 'Unknown'
  };
  
  console.error('Error logged:', errorInfo);
  
  // In production, you might want to send this to an error tracking service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to error tracking service
    // sendToErrorTrackingService(errorInfo);
  }
}

/**
 * Check if error is a startsWith related error
 */
export function isStartsWithError(error: unknown): boolean {
  if (!error || typeof error !== 'object') {
    return false;
  }
  
  const errorMessage = (error as Error).message || '';
  
  return errorMessage.includes("Cannot read properties of undefined (reading 'startsWith')") ||
         errorMessage.includes("Cannot read property 'startsWith' of undefined");
}

/**
 * Check if error should redirect to 404
 */
export function shouldRedirectTo404(error: unknown): boolean {
  if (!error || typeof error !== 'object') {
    return false;
  }
  
  const errorMessage = (error as Error).message || '';
  const errorName = (error as Error).name || '';
  
  // Check for startsWith errors
  if (isStartsWithError(error)) {
    return true;
  }
  
  // Check for other undefined property access errors
  if (errorName === 'TypeError' && 
      (errorMessage.includes('Cannot read properties of undefined') ||
       errorMessage.includes('Cannot read property') ||
       errorMessage.includes('undefined is not an object'))) {
    return true;
  }
  
  return false;
}

/**
 * Safe redirect function
 */
export function safeRedirectTo404() {
  if (typeof window !== 'undefined') {
    // Use replace to avoid adding to history
    window.location.replace('/not-found');
  }
}

/**
 * Cleanup global error handlers
 */
export function cleanupGlobalErrorHandlers() {
  if (typeof window === 'undefined') {
    return;
  }
  
  // Remove event listeners if needed
  // Note: In most cases, you don't need to remove these as they should persist
  // throughout the app lifecycle
}
