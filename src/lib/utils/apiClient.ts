/**
 * Centralized API client to handle all API requests through proxy routes
 * This helps avoid CORS issues by routing all requests through Next.js API routes
 */

interface ApiClientOptions extends RequestInit {
  token?: string;
  timeout?: number;
}

class ApiClient {
  private baseUrl: string;

  constructor() {
    // Use proxy routes instead of direct API calls
    this.baseUrl = '/api/proxy';
  }

  /**
   * Make a GET request
   */
  async get<T>(endpoint: string, options: ApiClientOptions = {}): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  /**
   * Make a POST request
   */
  async post<T>(endpoint: string, data?: any, options: ApiClientOptions = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a PUT request
   */
  async put<T>(endpoint: string, data?: any, options: ApiClientOptions = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a PATCH request
   */
  async patch<T>(endpoint: string, data?: any, options: ApiClientOptions = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a DELETE request
   */
  async delete<T>(endpoint: string, options: ApiClientOptions = {}): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  /**
   * Upload files using FormData
   */
  async upload<T>(endpoint: string, formData: FormData, options: ApiClientOptions = {}): Promise<T> {
    const { headers, ...restOptions } = options;
    
    // Don't set Content-Type for FormData, let the browser set it
    return this.request<T>(endpoint, {
      ...restOptions,
      method: 'POST',
      body: formData,
      headers: {
        ...headers,
        // Remove Content-Type to let browser set it with boundary
      },
    });
  }

  /**
   * Core request method
   */
  private async request<T>(endpoint: string, options: ApiClientOptions = {}): Promise<T> {
    const { token, timeout = 30000, ...fetchOptions } = options;

    // Ensure endpoint starts with /
    const normalizedEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    const url = `${this.baseUrl}/${normalizedEndpoint}`;

    // Prepare headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...fetchOptions.headers,
    };

    // Add authorization header if token is provided
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    // Remove Content-Type for FormData requests
    if (fetchOptions.body instanceof FormData) {
      delete headers['Content-Type'];
    }

    // Setup timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...fetchOptions,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        let errorData = null;

        try {
          errorData = await response.json();
          errorMessage = errorData.message || errorData.error || errorMessage;
        } catch {
          // Response is not JSON, use status text
        }

        throw new Error(errorMessage);
      }

      // Handle empty responses
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return response.json();
      } else {
        return response.text() as unknown as T;
      }

    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Request timeout');
        }
        throw error;
      }

      throw new Error('Unknown error occurred');
    }
  }

  /**
   * Get the current base URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }

  /**
   * Check if the API is available
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.get('/health');
      return true;
    } catch {
      return false;
    }
  }
}

// Export a singleton instance
export const apiClient = new ApiClient();

// Export the class for custom instances if needed
export { ApiClient };

// Convenience functions for common operations
export const api = {
  get: <T>(endpoint: string, token?: string) => 
    apiClient.get<T>(endpoint, { token }),
  
  post: <T>(endpoint: string, data?: any, token?: string) => 
    apiClient.post<T>(endpoint, data, { token }),
  
  put: <T>(endpoint: string, data?: any, token?: string) => 
    apiClient.put<T>(endpoint, data, { token }),
  
  patch: <T>(endpoint: string, data?: any, token?: string) => 
    apiClient.patch<T>(endpoint, data, { token }),
  
  delete: <T>(endpoint: string, token?: string) => 
    apiClient.delete<T>(endpoint, { token }),
  
  upload: <T>(endpoint: string, formData: FormData, token?: string) => 
    apiClient.upload<T>(endpoint, formData, { token }),
};

export default apiClient;
