/**
 * Proxy-aware fetch utility
 * Handles proxy authentication and configuration
 */

interface ProxyConfig {
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  protocol?: 'http' | 'https';
}

interface FetchOptions extends RequestInit {
  proxy?: ProxyConfig;
  timeout?: number;
}

/**
 * Parse proxy URL from environment variables
 */
function parseProxyUrl(proxyUrl: string): ProxyConfig | null {
  try {
    const url = new URL(proxyUrl);
    return {
      protocol: url.protocol.replace(':', '') as 'http' | 'https',
      host: url.hostname,
      port: url.port ? parseInt(url.port) : undefined,
      username: url.username || undefined,
      password: url.password || undefined,
    };
  } catch {
    return null;
  }
}

/**
 * Get proxy configuration from environment
 */
function getProxyConfig(): ProxyConfig | null {
  const httpProxy = process.env.HTTP_PROXY || process.env.http_proxy;
  const httpsProxy = process.env.HTTPS_PROXY || process.env.https_proxy;
  
  // Use HTTPS proxy for HTTPS requests, HTTP proxy for HTTP requests
  const proxyUrl = httpsProxy || httpProxy;
  
  if (!proxyUrl) {
    return null;
  }
  
  return parseProxyUrl(proxyUrl);
}

/**
 * Check if URL should bypass proxy
 */
function shouldBypassProxy(url: string): boolean {
  const noProxy = process.env.NO_PROXY || process.env.no_proxy;
  
  if (!noProxy) {
    return false;
  }
  
  const noProxyList = noProxy.split(',').map(item => item.trim());
  const urlObj = new URL(url);
  
  return noProxyList.some(pattern => {
    if (pattern === '*') return true;
    if (pattern.startsWith('.')) {
      return urlObj.hostname.endsWith(pattern);
    }
    return urlObj.hostname === pattern;
  });
}

/**
 * Enhanced fetch with proxy support
 */
export async function proxyFetch(
  url: string | URL,
  options: FetchOptions = {}
): Promise<Response> {
  const { proxy, timeout = 30000, ...fetchOptions } = options;
  
  // Check if we should bypass proxy
  if (shouldBypassProxy(url.toString())) {
    return fetch(url, fetchOptions);
  }
  
  // Get proxy configuration
  const proxyConfig = proxy || getProxyConfig();
  
  // If no proxy configuration, use regular fetch
  if (!proxyConfig) {
    return fetch(url, fetchOptions);
  }
  
  // Add proxy authentication headers if credentials are provided
  if (proxyConfig.username && proxyConfig.password) {
    const auth = btoa(`${proxyConfig.username}:${proxyConfig.password}`);
    fetchOptions.headers = {
      ...fetchOptions.headers,
      'Proxy-Authorization': `Basic ${auth}`,
    };
  }
  
  // Add timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  
  try {
    const response = await fetch(url, {
      ...fetchOptions,
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

/**
 * Proxy-aware API request utility
 */
export async function proxyApiRequest<T>(
  endpoint: string,
  options: FetchOptions = {}
): Promise<T> {
  const apiUrl = process.env.NEXT_PUBLIC_API_URL;
  
  if (!apiUrl) {
    throw new Error('API URL not configured');
  }
  
  const url = `${apiUrl}${endpoint}`;
  
  const defaultOptions: FetchOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };
  
  const response = await proxyFetch(url, defaultOptions);
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  return response.json();
}

/**
 * Test proxy connectivity
 */
export async function testProxyConnection(): Promise<{
  success: boolean;
  error?: string;
  proxyUsed?: boolean;
}> {
  try {
    const proxyConfig = getProxyConfig();
    const testUrl = 'https://httpbin.org/ip';
    
    const response = await proxyFetch(testUrl, {
      method: 'GET',
      timeout: 10000,
    });
    
    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        proxyUsed: !!proxyConfig,
      };
    } else {
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        proxyUsed: !!proxyConfig,
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      proxyUsed: !!getProxyConfig(),
    };
  }
}
