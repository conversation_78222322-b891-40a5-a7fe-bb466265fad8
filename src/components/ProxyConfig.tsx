"use client";

import React, { useState, useEffect } from 'react';
import { testProxyConnection } from '@/lib/utils/proxyFetch';

interface ProxyStatus {
  success: boolean;
  error?: string;
  proxyUsed?: boolean;
  testing?: boolean;
}

export const ProxyConfig: React.FC = () => {
  const [proxyStatus, setProxyStatus] = useState<ProxyStatus>({ success: false });
  const [showConfig, setShowConfig] = useState(false);

  const testConnection = async () => {
    setProxyStatus({ success: false, testing: true });
    
    try {
      const result = await testProxyConnection();
      setProxyStatus(result);
    } catch (error) {
      setProxyStatus({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        testing: false,
      });
    }
  };

  useEffect(() => {
    // Test connection on component mount
    testConnection();
  }, []);

  const getStatusColor = () => {
    if (proxyStatus.testing) return 'text-yellow-600';
    return proxyStatus.success ? 'text-green-600' : 'text-red-600';
  };

  const getStatusText = () => {
    if (proxyStatus.testing) return 'Testing connection...';
    if (proxyStatus.success) {
      return `Connection successful ${proxyStatus.proxyUsed ? '(via proxy)' : '(direct)'}`;
    }
    return `Connection failed: ${proxyStatus.error}`;
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Network Configuration</h3>
        <button
          onClick={() => setShowConfig(!showConfig)}
          className="text-blue-600 hover:text-blue-800 text-sm"
        >
          {showConfig ? 'Hide' : 'Show'} Details
        </button>
      </div>

      <div className="space-y-3">
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${
            proxyStatus.testing 
              ? 'bg-yellow-500 animate-pulse' 
              : proxyStatus.success 
                ? 'bg-green-500' 
                : 'bg-red-500'
          }`}></div>
          <span className={getStatusColor()}>{getStatusText()}</span>
        </div>

        <button
          onClick={testConnection}
          disabled={proxyStatus.testing}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {proxyStatus.testing ? 'Testing...' : 'Test Connection'}
        </button>

        {showConfig && (
          <div className="mt-4 p-3 bg-white rounded border">
            <h4 className="font-medium mb-2">Environment Variables</h4>
            <div className="space-y-2 text-sm font-mono">
              <div>
                <span className="text-gray-600">HTTP_PROXY:</span>{' '}
                <span className="text-blue-600">
                  {process.env.HTTP_PROXY || 'Not set'}
                </span>
              </div>
              <div>
                <span className="text-gray-600">HTTPS_PROXY:</span>{' '}
                <span className="text-blue-600">
                  {process.env.HTTPS_PROXY || 'Not set'}
                </span>
              </div>
              <div>
                <span className="text-gray-600">NO_PROXY:</span>{' '}
                <span className="text-blue-600">
                  {process.env.NO_PROXY || 'Not set'}
                </span>
              </div>
              <div>
                <span className="text-gray-600">API_URL:</span>{' '}
                <span className="text-blue-600">
                  {process.env.NEXT_PUBLIC_API_URL || 'Not set'}
                </span>
              </div>
            </div>

            <div className="mt-4">
              <h5 className="font-medium mb-2">Proxy Configuration Help</h5>
              <div className="text-sm text-gray-600 space-y-1">
                <p>If you're behind a corporate proxy, you may need to:</p>
                <ol className="list-decimal list-inside space-y-1 ml-2">
                  <li>Contact your IT department for proxy credentials</li>
                  <li>Set environment variables with your proxy settings</li>
                  <li>Add proxy configuration to your .env file</li>
                </ol>
                
                <div className="mt-3 p-2 bg-gray-100 rounded">
                  <p className="font-medium">Example .env configuration:</p>
                  <pre className="text-xs mt-1">
{`HTTP_PROXY=http://username:<EMAIL>:8080
HTTPS_PROXY=http://username:<EMAIL>:8080
NO_PROXY=localhost,127.0.0.1,.local`}
                  </pre>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProxyConfig;
