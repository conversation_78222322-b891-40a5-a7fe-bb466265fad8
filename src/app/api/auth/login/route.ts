import { NextResponse } from 'next/server'

export async function POST(request: Request) {
    try {
        const body = await request.json()
        const { email, password } = body

        // Forward the request to the actual backend API
        const apiUrl = process.env.NEXT_PUBLIC_API_URL
        if (!apiUrl) {
            return NextResponse.json(
                { error: 'API URL not configured' },
                { status: 500 }
            )
        }

        const response = await fetch(`${apiUrl}auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, password }),
        })

        const data = await response.json()

        // Return the response from the backend API
        return NextResponse.json(data, { status: response.status })

    } catch (error) {
        console.error('Login API error:', error)
        return NextResponse.json(
            { error: 'Authentication failed' },
            { status: 500 }
        )
    }
}