import { NextRequest, NextResponse } from 'next/server'

// Handle all HTTP methods
export async function GET(
  request: NextRe<PERSON>,
  { params }: { params: { path: string[] } }
) {
  return handleProxyRequest(request, params.path, 'GET')
}

export async function POST(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleProxyRequest(request, params.path, 'POST')
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleProxyRequest(request, params.path, 'PUT')
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleProxyRequest(request, params.path, 'DELETE')
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleProxyRequest(request, params.path, 'PATCH')
}

async function handleProxyRequest(
  request: NextRequest,
  pathSegments: string[],
  method: string
) {
  try {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL
    if (!apiUrl) {
      return NextResponse.json(
        { error: 'API URL not configured' },
        { status: 500 }
      )
    }

    // Construct the target URL
    const targetPath = pathSegments.join('/')
    const targetUrl = `${apiUrl}${targetPath}`
    
    // Get search params from the original request
    const searchParams = request.nextUrl.searchParams
    const queryString = searchParams.toString()
    const fullTargetUrl = queryString ? `${targetUrl}?${queryString}` : targetUrl

    // Prepare headers (exclude host and other problematic headers)
    const headers: HeadersInit = {}
    
    // Copy relevant headers from the original request
    const allowedHeaders = [
      'content-type',
      'authorization',
      'accept',
      'user-agent',
      'x-requested-with'
    ]
    
    allowedHeaders.forEach(headerName => {
      const headerValue = request.headers.get(headerName)
      if (headerValue) {
        headers[headerName] = headerValue
      }
    })

    // Prepare request options
    const requestOptions: RequestInit = {
      method,
      headers,
    }

    // Add body for methods that support it
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      try {
        const body = await request.text()
        if (body) {
          requestOptions.body = body
        }
      } catch (error) {
        console.warn('Could not read request body:', error)
      }
    }

    // Make the request to the backend API
    const response = await fetch(fullTargetUrl, requestOptions)
    
    // Get response data
    const responseData = await response.text()
    
    // Create response with proper headers
    const proxyResponse = new NextResponse(responseData, {
      status: response.status,
      statusText: response.statusText,
    })

    // Copy relevant response headers
    const responseHeaders = [
      'content-type',
      'cache-control',
      'expires',
      'last-modified',
      'etag'
    ]
    
    responseHeaders.forEach(headerName => {
      const headerValue = response.headers.get(headerName)
      if (headerValue) {
        proxyResponse.headers.set(headerName, headerValue)
      }
    })

    // Add CORS headers
    proxyResponse.headers.set('Access-Control-Allow-Origin', '*')
    proxyResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS')
    proxyResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')

    return proxyResponse

  } catch (error) {
    console.error('Proxy request failed:', error)
    return NextResponse.json(
      { 
        error: 'Proxy request failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
      'Access-Control-Max-Age': '86400',
    },
  })
}
