import { ApiResponse } from "@/types/pengajuan/pengajuan";

// Fungsi untuk mengambil data dari API
export const fetchPengajuanData = async (page: number, token: string): Promise<ApiResponse['data']> => {
    const res = await fetch(`/api/proxy/submissions/tanaman?page=${page}`, {
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });
    const data = await res.json();
    return data.data;
};

// Fungsi untuk menambahkan data ke API
export const addPengajuanData = async (payload: Object, token: string): Promise<Response> => {
    const res = await fetch(`/api/proxy/submissions/tanaman`, {
        method: 'POST',
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
    });
    const data = await res;
    return data;
}

// Fungsi untuk edit data ke API
export const editPengajuanData = async (payload: Object, id:string,token: string): Promise<Response> => {
    const res = await fetch(`/api/proxy/submissions/tanaman/${id}`, {
        method: 'PUT',
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
    });
    const data = await res;
    return data;
}

export const updateStatusPengajuanData = async (payload: Object, id:string,token: string): Promise<Response> => {
    const res = await fetch(`/api/proxy/submissions/tanaman/status/${id}`, {
        method: 'PUT',
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
    });
    const data = await res;
    return data;
}

// Fungsi untuk mengambil data dari API
export const fetchPengajuanDataById = async (id:string, token: string): Promise<ApiResponse['data']> => {
    const res = await fetch(`/api/proxy/submissions/tanaman/${id}`, {
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });
    const data = await res.json();
    return data.data;
};